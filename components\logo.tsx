"use client"

import Image from "next/image"

interface LogoProps {
  size?: number
  className?: string
  priority?: boolean
}

export default function Logo({ size = 40, className = "", priority = true }: LogoProps) {
  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Image 
        src="/logo.png" 
        alt="NoisyPay Logo" 
        width={size} 
        height={size} 
        className="rounded-lg" 
        priority={priority}
        sizes={`${size}px`}
        quality={90}
        style={{ width: 'auto', height: 'auto' }}
      />
      <span className="font-bold text-lg text-gray-900">NoisyPay</span>
    </div>
  )
}

export function LogoIcon({ size = 40, className = "", priority = true }: LogoProps) {
  return (
    <Image 
      src="/logo-icon.png" 
      alt="NoisyPay" 
      width={size} 
      height={size} 
      className={`rounded-lg ${className}`} 
      priority={priority}
      sizes={`${size}px`}
      quality={90}
      style={{ width: 'auto', height: 'auto' }}
    />
  )
}
